#!/usr/bin/env ruby

# Simple test script to diagnose and execute GoCardless sync
puts "=== GOCARDLESS SYNC TEST SCRIPT ==="
puts "Start Time: #{Time.now}"

begin
  # Test 1: Basic Ruby functionality
  puts "\n1. Testing basic Ruby functionality..."
  puts "✓ Ruby version: #{RUBY_VERSION}"
  puts "✓ Current directory: #{Dir.pwd}"
  
  # Test 2: Rails environment loading
  puts "\n2. Loading Rails environment..."
  require_relative 'config/environment'
  puts "✓ Rails loaded successfully"
  puts "✓ Rails version: #{Rails.version}"
  puts "✓ Environment: #{Rails.env}"
  
  # Test 3: Database connectivity
  puts "\n3. Testing database connectivity..."
  user_count = User.count rescue "ERROR"
  puts "✓ Database connection: #{user_count.is_a?(Integer) ? 'OK' : 'FAILED'}"
  puts "✓ User count: #{user_count}"
  
  # Test 4: GoCardless provider
  puts "\n4. Testing GoCardless provider..."
  provider = Provider::Registry.get_provider(:gocardless)
  puts "✓ Provider class: #{provider.class.name}"
  puts "✓ Provider healthy: #{provider.healthy?}"
  
  # Test 5: Starling Bank account
  puts "\n5. Testing Starling Bank account access..."
  starling_item = GocardlessItem.find('9ebc7d2c-08bf-4d07-8c7f-67ab339f4699')
  gocardless_account = starling_item.gocardless_accounts.first
  puts "✓ Starling item found: #{starling_item.name}"
  puts "✓ Account found: #{gocardless_account.account.name}"
  puts "✓ Current entries: #{gocardless_account.account.entries.count}"
  
  # Test 6: Bank factory
  puts "\n6. Testing bank factory..."
  bank_handler = GocardlessBankFactory.for_institution('STARLING_SRLGGB3L')
  puts "✓ Bank handler: #{bank_handler.class.name}"
  puts "✓ Is Starling handler: #{bank_handler.is_a?(GocardlessBankFactory::StarlingBankHandler)}"
  
  # Test 7: Institution lookup (1 API call)
  puts "\n7. Testing institution lookup (1 API call)..."
  institution_response = provider.get_institution('STARLING_SRLGGB3L')
  if institution_response.success?
    institution = institution_response.data
    puts "✓ Institution lookup successful"
    puts "✓ Institution: #{institution['name']}"
    puts "✓ Transaction days: #{institution['transaction_total_days']}"
    
    max_days = bank_handler.get_maximum_historical_days(institution)
    start_date = Date.current - max_days.days
    puts "✓ Calculated max days: #{max_days}"
    puts "✓ Start date: #{start_date}"
    puts "✓ Date range: #{start_date} to #{Date.current}"
  else
    puts "✗ Institution lookup failed: #{institution_response.error&.message}"
    exit 1
  end
  
  puts "\n=== ALL TESTS PASSED - PROCEEDING WITH SYNC ==="
  
  # Execute the sync
  puts "\n8. Executing GoCardless sync..."
  sync_start = Time.now
  
  initial_count = gocardless_account.account.entries.count
  puts "✓ Initial entry count: #{initial_count}"
  
  # Run the sync
  gocardless_account.sync_data
  
  sync_end = Time.now
  sync_duration = (sync_end - sync_start).round(2)
  
  # Check results
  gocardless_account.account.reload
  final_count = gocardless_account.account.entries.count
  new_entries = final_count - initial_count
  
  puts "✓ Sync completed in #{sync_duration} seconds"
  puts "✓ New entries created: #{new_entries}"
  
  if new_entries > 0
    transaction_entries = gocardless_account.account.entries
      .joins(:entryable)
      .where(entryable_type: 'Transaction')
      .order(:date)
    
    if transaction_entries.any?
      oldest = transaction_entries.first
      newest = transaction_entries.last
      days_imported = (newest.date - oldest.date).to_i
      
      puts "✓ Transaction count: #{transaction_entries.count}"
      puts "✓ Date range: #{oldest.date} to #{newest.date}"
      puts "✓ Days imported: #{days_imported}"
      
      if days_imported >= 700
        puts "🎉 SUCCESS: Imported #{days_imported} days of history!"
      else
        puts "⚠️ WARNING: Only imported #{days_imported} days"
      end
      
      # Show sample transactions
      puts "\n--- Sample Transactions ---"
      transaction_entries.first(3).each do |entry|
        amount = entry.amount >= 0 ? "+£#{entry.amount}" : "-£#{entry.amount.abs}"
        puts "  #{entry.date}: #{amount} - #{entry.name[0..40]}..."
      end
    end
    
    balance = gocardless_account.account.balance
    puts "✓ Account balance: £#{balance}"
  else
    puts "⚠️ WARNING: No entries created"
  end
  
rescue => e
  puts "\n❌ ERROR: #{e.class.name}: #{e.message}"
  puts "Backtrace:"
  e.backtrace.first(5).each { |line| puts "  #{line}" }
  exit 1
end

puts "\n=== SYNC TEST COMPLETE ==="
puts "End Time: #{Time.now}"
