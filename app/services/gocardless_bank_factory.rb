# GoCardless Bank Factory
# Handles bank-specific transaction processing and normalization
# Based on Actual Budget's bank factory pattern

class GocardlessBankFactory
  # Banks with limited transaction history (from Actual Budget)
  BANKS_WITH_LIMITED_HISTORY = [
    'ABANCA_CAGLESMM',
    'AIRBANK_AIRACZPP',
    'BANCA_AIDEXA_AIDXITMM',
    'BANCA_PATRIMONI_SENVITT1',
    'BANCA_SELLA_SELBIT2B',
    'BANK_MILLENNIUM_BIGBPLPW',
    'BANKINTER_BKBKESMM',
    'BBVA_BBVAESMM',
    'BELFIUS_GKCCBEBB',
    'BNP_BE_GEBABEBB',
    'BNP_PL_PPABPLPK',
    'BOURSORAMA_BOUSFRPP',
    'BRED_BREDFRPPXXX',
    'CAIXA_GERAL_DEPOSITOS_CGDIPTPL',
    'CAIXABANK_CAIXESBB',
    'CARTALIS_CIMTITR1',
    'CESKA_SPORITELNA_LONG_GIBACZPX',
    'COOP_EKRDEE22',
    'DKB_BYLADEM1',
    'DNB_DNBANOKK',
    'DOTS_HYEEIT22',
    'FINECO_FEBIITM2XXX',
    'FINECO_UK_FEBIITM2XXX',
    'FORTUNEO_FTNOFRP1XXX',
    'GLS_GEMEINSCHAFTSBANK_GENODEM1GLS',
    'HYPE_BUSINESS_HYEEIT22',
    'HYPE_HYEEIT22',
    'ILLIMITY_ITTPIT2M',
    'INDUSTRA_MULTLV2X',
    'INDUSTRIEL_CMCIFRPAXXX',
    'ING_PL_INGBPLPW',
    'JEKYLL_JEYKLL002',
    'KBC_KREDBEBB',
    'KBC_BRUSSELS_KREDBEBB',
    'LABORALKUTXA_CLPEES2M',
    'LCL_CRLYFRPP',
    'LHV_LHVBEE22',
    'LUMINOR_AGBLLT2X',
    'LUMINOR_NDEAEE2X',
    'LUMINOR_NDEALT2X',
    'LUMINOR_NDEALV2X',
    'LUMINOR_RIKOEE22',
    'LUMINOR_RIKOLV2X',
    'MBANK_RETAIL_BREXPLPW',
    'MEDICINOSBANK_MDBALT22XXX',
    'NORDEA_NDEADKKK',
    'N26_NTSBDEB1',
    'OPYN_BITAITRRB2B',
    'PAYTIPPER_PAYTITM1',
    'QONTO_QNTOFRP1',
    'REVOLUT_REVOLT21',
    'SANTANDER_BSCHESMM',
    'SANTANDER_DE_SCFBDE33',
    'SEB_CBVILT2X',
    'SEB_EEUHEE2X',
    'SEB_UNLALV2X',
    'SELLA_PERSONAL_CREDIT_SELBIT22',
    'BANCOACTIVOBANK_ACTVPTPL',
    'SMARTIKA_SELBIT22',
    'SWEDBANK_HABAEE2X',
    'SWEDBANK_HABALT22',
    'SWEDBANK_HABALV22',
    'SWEDBANK_SWEDSESS',
    'TIM_HYEEIT22',
    'TOT_SELBIT2B',
    'VUB_BANKA_SUBASKBX'
  ].freeze

  def self.for_institution(institution_id)
    case institution_id
    when 'STARLING_SRLGGB3L'
      StarlingBankHandler.new
    else
      DefaultBankHandler.new
    end
  end

  def self.has_limited_history?(institution_id)
    BANKS_WITH_LIMITED_HISTORY.include?(institution_id)
  end

  # Base bank handler class
  class BaseBankHandler
    def normalize_transaction(transaction_data, booked: true)
      # Default normalization - can be overridden by specific banks
      {
        id: transaction_data["transactionId"],
        amount: transaction_data.dig("transactionAmount", "amount").to_f,
        currency: transaction_data.dig("transactionAmount", "currency"),
        date: parse_transaction_date(transaction_data),
        description: normalize_description(transaction_data, booked: booked),
        booked: booked,
        raw_data: transaction_data
      }
    end

    def sort_transactions(transactions)
      # Sort by date, oldest first
      transactions.sort_by do |transaction|
        date_str = transaction["valueDate"] || transaction["bookingDate"]
        Date.parse(date_str) rescue Date.current
      end
    end

    def calculate_starting_balance(transactions, balances)
      # Default starting balance calculation
      current_balance = balances.find { |b| b["balanceType"] == "interimAvailable" }
      return 0.0 unless current_balance.present?

      current_amount = current_balance.dig("balanceAmount", "amount").to_f
      
      # Sum all transaction amounts
      transaction_total = transactions.sum { |t| t.dig("transactionAmount", "amount").to_f }
      
      # Starting balance = current balance - all transactions
      current_amount - transaction_total
    end

    def get_maximum_historical_days(institution_data)
      # Default logic for determining maximum historical days
      transaction_total_days = institution_data['transaction_total_days'].to_i
      
      if transaction_total_days > 0
        transaction_total_days
      else
        # Fallback to 2 years if no limit specified
        730
      end
    end

    private

    def parse_transaction_date(transaction_data)
      date_str = transaction_data["valueDate"] || transaction_data["bookingDate"]
      Date.parse(date_str) rescue Date.current
    end

    def normalize_description(transaction_data, booked: true)
      description = transaction_data["remittanceInformationUnstructured"] || "GoCardless Transaction"
      booked ? description : "[PENDING] #{description}"
    end
  end

  # Default handler for most banks
  class DefaultBankHandler < BaseBankHandler
    # Uses all default implementations
  end

  # Starling Bank specific handler
  class StarlingBankHandler < BaseBankHandler
    def normalize_transaction(transaction_data, booked: true)
      # Starling Bank specific normalization
      normalized = super(transaction_data, booked: booked)
      
      # Starling Bank specific adjustments
      normalized[:description] = clean_starling_description(normalized[:description])
      
      normalized
    end

    def get_maximum_historical_days(institution_data)
      # Starling Bank supports 730 days (2 years) of history
      # Override to ensure we always request the full amount
      transaction_total_days = institution_data['transaction_total_days'].to_i
      
      if transaction_total_days >= 730
        730 # Use full 2 years
      else
        transaction_total_days # Use whatever the institution allows
      end
    end

    private

    def clean_starling_description(description)
      # Clean up Starling Bank specific description formatting
      # Remove common prefixes/suffixes that aren't useful
      cleaned = description.to_s.strip
      
      # Remove common Starling Bank prefixes
      cleaned = cleaned.gsub(/^(FASTER PAYMENT|FP|CARD PAYMENT|CP)\s+/i, '')
      
      # Remove reference numbers that aren't useful
      cleaned = cleaned.gsub(/\s+REF:\s*\w+$/i, '')
      
      cleaned.strip
    end
  end
end
