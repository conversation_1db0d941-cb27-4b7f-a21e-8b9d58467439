class GocardlessAccount < ApplicationRecord
  include Syncable

  validates :gocardless_account_id, presence: true, uniqueness: true

  belongs_to :gocardless_item
  belongs_to :account
  has_one :family, through: :gocardless_item

  def sync_data
    return unless provider.present?

    Rails.logger.info "Syncing GoCardless account: #{gocardless_account_id}"

    # Sync balances first
    sync_balances

    # Add delay to avoid rate limiting
    sleep(1)

    # Sync transactions
    sync_transactions

    Rails.logger.info "Completed syncing GoCardless account: #{gocardless_account_id}"
  rescue => error
    Rails.logger.error "Error syncing GoCardless account #{gocardless_account_id}: #{error.message}"
    raise error
  end

  private
    def provider
      @provider ||= if gocardless_item.provider_name.present?
        # Use the specific provider that created this account
        Provider::Registry.get_specific_gocardless_provider(gocardless_item.provider_name)
      else
        # Fallback to manager for legacy accounts without provider tracking
        Rails.logger.warn "GocardlessAccount #{id} has no provider_name, using manager (may cause authentication issues)"
        Provider::Registry.get_provider(:gocardless)
      end
    end

    def sync_balances
      Rails.logger.info "Syncing balances for account: #{gocardless_account_id}"

      balances_response = provider.get_account_balances(gocardless_account_id)

      unless balances_response.success?
        if balances_response.error&.message&.include?("429") || balances_response.error&.message&.include?("rate limit")
          Rails.logger.warn "Rate limited - skipping balance sync for now: #{balances_response.error&.message}"
          log_rate_limit_info(balances_response)
        else
          Rails.logger.error "Failed to fetch balances: #{balances_response.error&.message}"
        end
        return
      end

      balances_data = balances_response.data["balances"] || []
      Rails.logger.info "Processing #{balances_data.count} balance entries"

      balances_data.each do |balance_data|
        create_balance_entry(balance_data)
      end
    end

    def sync_transactions
      Rails.logger.info "Syncing transactions for account: #{gocardless_account_id}"

      # Determine date range for sync
      date_from = determine_sync_start_date
      date_to = Date.current

      Rails.logger.info "Fetching transactions from #{date_from} to #{date_to} (#{(date_to - date_from).to_i} days)"

      transactions_response = provider.get_account_transactions(
        gocardless_account_id,
        date_from: date_from,
        date_to: date_to
      )

      unless transactions_response.success?
        error_message = transactions_response.error&.message.to_s

        if error_message.include?("429") || error_message.include?("rate limit")
          Rails.logger.warn "Rate limited - skipping transaction sync for now: #{error_message}"
          log_rate_limit_info(transactions_response)
        elsif error_message.include?("400") && error_message.include?("date")
          Rails.logger.warn "Date range invalid - trying shorter period: #{error_message}"
          # Try with a shorter period (90 days) if the full range fails
          fallback_date_from = [date_from, Date.current - 90.days].max
          Rails.logger.info "Retrying with fallback date range: #{fallback_date_from} to #{date_to}"

          fallback_response = provider.get_account_transactions(
            gocardless_account_id,
            date_from: fallback_date_from,
            date_to: date_to
          )

          if fallback_response.success?
            transactions_response = fallback_response
          else
            Rails.logger.error "Fallback sync also failed: #{fallback_response.error&.message}"
            return
          end
        else
          Rails.logger.error "Failed to fetch transactions: #{error_message}"
          return
        end
      end

      # Process both booked and pending transactions
      transactions_data = transactions_response.data&.dig("transactions") || {}
      booked_transactions = transactions_data["booked"] || []
      pending_transactions = transactions_data["pending"] || []

      Rails.logger.info "Processing #{booked_transactions.count} booked and #{pending_transactions.count} pending transactions"

      # Sort transactions by date (oldest first for consistent processing)
      sorted_booked = sort_transactions(booked_transactions)
      sorted_pending = sort_transactions(pending_transactions)

      # Process booked transactions
      sorted_booked.each do |transaction_data|
        create_transaction_entry(transaction_data, booked: true)
      end

      # Process pending transactions (optional - you may want to skip these)
      sorted_pending.each do |transaction_data|
        create_transaction_entry(transaction_data, booked: false)
      end

      # Update last sync date for incremental syncs
      update_last_transaction_sync_date(date_to)
    end

    def create_balance_entry(balance_data)
      # GoCardless balance structure:
      # {
      #   "balanceAmount": { "amount": "1000.00", "currency": "GBP" },
      #   "balanceType": "interimAvailable",
      #   "referenceDate": "2023-01-01"
      # }

      amount_data = balance_data["balanceAmount"]
      return unless amount_data.present?

      amount = amount_data["amount"].to_f
      currency = amount_data["currency"] || account.currency
      date = Date.parse(balance_data["referenceDate"]) rescue Date.current

      # Create a valuation entry to set the account balance
      account.entries.find_or_create_by(
        date: date,
        entryable_type: "Valuation"
      ) do |entry|
        entry.amount = amount # Use the balance amount directly
        entry.currency = currency
        entry.name = "Balance from GoCardless"
        entry.entryable = Valuation.new
      end
    end

    def create_transaction_entry(transaction_data, booked: true)
      # GoCardless transaction structure:
      # {
      #   "transactionId": "unique-id",
      #   "transactionAmount": { "amount": "-50.00", "currency": "GBP" },
      #   "valueDate": "2023-01-01",
      #   "bookingDate": "2023-01-01",
      #   "remittanceInformationUnstructured": "Payment description"
      # }

      transaction_id = transaction_data["transactionId"]
      amount_data = transaction_data["transactionAmount"]

      return unless transaction_id.present? && amount_data.present?

      amount = amount_data["amount"].to_f
      currency = amount_data["currency"] || account.currency
      date = Date.parse(transaction_data["valueDate"] || transaction_data["bookingDate"]) rescue Date.current
      description = transaction_data["remittanceInformationUnstructured"] || "GoCardless Transaction"

      # Add pending indicator to description if not booked
      description = "[PENDING] #{description}" unless booked

      # Check if transaction already exists
      existing_transaction = account.transactions.find_by(gocardless_transaction_id: transaction_id)

      if existing_transaction.present?
        Rails.logger.debug "Transaction #{transaction_id} already exists, skipping"
        return
      end

      begin
        # Create transaction entry
        entry = account.entries.create!(
          date: date,
          amount: amount, # GoCardless already provides signed amounts
          currency: currency,
          name: description,
          entryable: Transaction.new(
            gocardless_transaction_id: transaction_id
          )
        )

        Rails.logger.debug "Created transaction: #{date} £#{amount} - #{description}"

      rescue => e
        Rails.logger.error "Failed to create transaction #{transaction_id}: #{e.message}"
        raise e
      end
    end

    def determine_sync_start_date
      # Check if this is the first sync (no transactions exist)
      latest_transaction = account.transactions
        .joins(:entry)
        .where.not(gocardless_transaction_id: nil)
        .order("entries.date DESC")
        .first

      if latest_transaction.nil?
        # First sync: get maximum historical data based on institution limits
        get_maximum_historical_start_date
      else
        # Incremental sync: get transactions from the last sync date
        # Go back 1 day to catch any late-posting transactions
        latest_transaction.entry.date - 1.day
      end
    end

    def get_maximum_historical_start_date
      begin
        # Get the institution details to find the actual transaction_total_days limit
        institution_response = provider.get_institution(gocardless_item.institution_id)

        if institution_response.success?
          institution = institution_response.data
          max_days = institution['transaction_total_days'].to_i

          if max_days > 0
            # Use the institution's actual limit
            Rails.logger.info "Institution allows #{max_days} days of history"
            Date.current - max_days.days
          else
            # Fallback to 2 years if no limit specified
            Rails.logger.warn "No transaction_total_days limit found, using 2 years"
            2.years.ago.to_date
          end
        else
          # Fallback to 2 years if we can't get institution details
          Rails.logger.warn "Could not get institution details, using 2 years: #{institution_response.error&.message}"
          2.years.ago.to_date
        end
      rescue => e
        # Fallback to 2 years if anything goes wrong
        Rails.logger.warn "Error getting institution limits, using 2 years: #{e.message}"
        2.years.ago.to_date
      end
    end

    def update_last_transaction_sync_date(date)
      # Store the last sync date for future reference
      # This could be stored in the gocardless_account record if needed
      Rails.logger.info "Last transaction sync completed for date: #{date}"
    end
end