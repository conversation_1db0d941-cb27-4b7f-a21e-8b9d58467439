namespace :gocardless do
  desc "Migrate existing GoCardless connections to use provider tracking"
  task migrate_provider_tracking: :environment do
    puts "🔄 Migrating existing GoCardless connections to use provider tracking..."
    
    # Find connections without provider_name
    connections_without_provider = GocardlessConnection.where(provider_name: nil)
    items_without_provider = GocardlessItem.where(provider_name: nil)
    
    puts "Found #{connections_without_provider.count} connections without provider tracking"
    puts "Found #{items_without_provider.count} items without provider tracking"
    
    if connections_without_provider.empty? && items_without_provider.empty?
      puts "✅ All connections already have provider tracking"
      next
    end
    
    # Get available providers
    provider_configs = Rails.application.config.gocardless_providers || []
    
    if provider_configs.empty?
      puts "❌ No GoCardless providers configured"
      next
    end
    
    primary_provider_name = provider_configs.first[:name]
    puts "Using primary provider '#{primary_provider_name}' for legacy connections"
    
    # Update connections
    connections_updated = 0
    connections_without_provider.find_each do |connection|
      begin
        connection.update!(provider_name: primary_provider_name)
        connections_updated += 1
        print "."
      rescue => e
        puts "\n❌ Failed to update connection #{connection.id}: #{e.message}"
      end
    end
    
    # Update items
    items_updated = 0
    items_without_provider.find_each do |item|
      begin
        item.update!(provider_name: primary_provider_name)
        items_updated += 1
        print "."
      rescue => e
        puts "\n❌ Failed to update item #{item.id}: #{e.message}"
      end
    end
    
    puts "\n✅ Migration completed:"
    puts "   - Updated #{connections_updated} connections"
    puts "   - Updated #{items_updated} items"
    puts "   - All legacy connections now use provider: #{primary_provider_name}"
  end
  
  desc "Check GoCardless provider tracking status"
  task check_provider_tracking: :environment do
    puts "🔍 Checking GoCardless provider tracking status..."
    
    total_connections = GocardlessConnection.count
    connections_with_provider = GocardlessConnection.where.not(provider_name: nil).count
    connections_without_provider = total_connections - connections_with_provider
    
    total_items = GocardlessItem.count
    items_with_provider = GocardlessItem.where.not(provider_name: nil).count
    items_without_provider = total_items - items_with_provider
    
    puts "\n📊 Provider Tracking Status:"
    puts "Connections:"
    puts "  - Total: #{total_connections}"
    puts "  - With provider tracking: #{connections_with_provider}"
    puts "  - Without provider tracking: #{connections_without_provider}"
    
    puts "\nItems:"
    puts "  - Total: #{total_items}"
    puts "  - With provider tracking: #{items_with_provider}"
    puts "  - Without provider tracking: #{items_without_provider}"
    
    if connections_without_provider > 0 || items_without_provider > 0
      puts "\n⚠️  Some connections/items need migration. Run:"
      puts "   rails gocardless:migrate_provider_tracking"
    else
      puts "\n✅ All connections have provider tracking"
    end
    
    # Show provider distribution
    if total_connections > 0
      puts "\n📈 Provider Distribution:"
      GocardlessConnection.group(:provider_name).count.each do |provider, count|
        provider_name = provider || "untracked"
        puts "  - #{provider_name}: #{count} connections"
      end
    end
  end
  
  desc "Test provider authentication for all tracked providers"
  task test_providers: :environment do
    puts "🧪 Testing GoCardless provider authentication..."
    
    provider_configs = Rails.application.config.gocardless_providers || []
    
    if provider_configs.empty?
      puts "❌ No GoCardless providers configured"
      next
    end
    
    provider_configs.each_with_index do |config, index|
      provider_name = config[:name]
      puts "\n🔑 Testing provider: #{provider_name}"
      
      begin
        provider = Provider::Registry.get_specific_gocardless_provider(provider_name)
        
        if provider.nil?
          puts "  ❌ Provider not found"
          next
        end
        
        # Test authentication
        response = provider.get_access_token
        
        if response.success?
          puts "  ✅ Authentication successful"
          
          # Test institutions endpoint
          institutions_response = provider.list_institutions(country: "GB")
          if institutions_response.success?
            institution_count = institutions_response.data.count
            puts "  ✅ Institutions endpoint working (#{institution_count} banks available)"
          else
            puts "  ⚠️  Institutions endpoint failed: #{institutions_response.error&.message}"
          end
        else
          puts "  ❌ Authentication failed: #{response.error&.message}"
        end
      rescue => e
        puts "  ❌ Error testing provider: #{e.message}"
      end
    end
  end
end
