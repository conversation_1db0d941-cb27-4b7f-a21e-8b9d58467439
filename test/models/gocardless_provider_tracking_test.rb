require "test_helper"

class GocardlessProviderTrackingTest < ActiveSupport::TestCase
  setup do
    @family = families(:dylan_family)
  end

  test "gocardless connection stores provider name" do
    connection = @family.gocardless_connections.build(
      requisition_id: "test-requisition-123",
      institution_id: "STARLING_SRLG",
      status: "LN",
      provider_name: "primary"
    )
    
    assert connection.save
    assert_equal "primary", connection.provider_name
  end

  test "gocardless item stores provider name" do
    item = @family.gocardless_items.build(
      name: "Test Bank",
      requisition_id: "test-requisition-456",
      institution_id: "STARLING_SRLG",
      provider_name: "backup"
    )
    
    assert item.save
    assert_equal "backup", item.provider_name
  end

  test "connection passes provider name to item" do
    connection = @family.gocardless_connections.create!(
      requisition_id: "test-requisition-789",
      institution_id: "STARLING_SRLG",
      status: "LN",
      provider_name: "backup2"
    )
    
    # Mock institution_name method
    connection.stubs(:institution_name).returns("Test Bank")
    
    item = connection.find_or_create_gocardless_item
    
    assert_equal "backup2", item.provider_name
    assert_equal connection.provider_name, item.provider_name
  end

  test "legacy connection gets assigned primary provider" do
    # Create a connection without provider_name (legacy)
    connection = @family.gocardless_connections.create!(
      requisition_id: "legacy-requisition-123",
      institution_id: "STARLING_SRLG",
      status: "LN"
      # No provider_name set
    )
    
    assert_nil connection.provider_name
    
    # Mock provider configuration
    Rails.application.config.stubs(:gocardless_providers).returns([
      { name: "primary", secret_id: "test", secret_key: "test" }
    ])
    
    connection.ensure_provider_tracking!
    connection.reload
    
    assert_equal "primary", connection.provider_name
  end

  test "legacy item gets assigned primary provider" do
    # Create an item without provider_name (legacy)
    item = @family.gocardless_items.create!(
      name: "Legacy Bank",
      requisition_id: "legacy-item-123",
      institution_id: "STARLING_SRLG"
      # No provider_name set
    )
    
    assert_nil item.provider_name
    
    # Mock provider configuration
    Rails.application.config.stubs(:gocardless_providers).returns([
      { name: "primary", secret_id: "test", secret_key: "test" }
    ])
    
    item.ensure_provider_tracking!
    item.reload
    
    assert_equal "primary", item.provider_name
  end

  test "scopes work correctly" do
    # Create connections with and without provider tracking
    with_provider = @family.gocardless_connections.create!(
      requisition_id: "with-provider-123",
      institution_id: "STARLING_SRLG",
      status: "LN",
      provider_name: "primary"
    )
    
    without_provider = @family.gocardless_connections.create!(
      requisition_id: "without-provider-123",
      institution_id: "STARLING_SRLG",
      status: "LN"
      # No provider_name
    )
    
    assert_includes GocardlessConnection.with_provider_tracking, with_provider
    assert_not_includes GocardlessConnection.with_provider_tracking, without_provider
    
    assert_includes GocardlessConnection.without_provider_tracking, without_provider
    assert_not_includes GocardlessConnection.without_provider_tracking, with_provider
  end

  test "provider method returns correct provider for tracked connection" do
    # Mock the provider registry
    mock_provider = mock('provider')
    Provider::Registry.stubs(:get_specific_gocardless_provider).with("backup").returns(mock_provider)
    
    item = @family.gocardless_items.create!(
      name: "Test Bank",
      requisition_id: "test-provider-123",
      institution_id: "STARLING_SRLG",
      provider_name: "backup"
    )
    
    account = @family.accounts.create!(
      name: "Test Account",
      accountable: Depository.new,
      balance: 1000
    )
    
    gocardless_account = item.gocardless_accounts.create!(
      gocardless_account_id: "test-account-123",
      account: account
    )
    
    # Test that the provider method returns the specific provider
    assert_equal mock_provider, gocardless_account.send(:provider)
  end

  test "provider method falls back to manager for legacy connection" do
    # Mock the provider registry
    mock_manager = mock('manager')
    Provider::Registry.stubs(:get_provider).with(:gocardless).returns(mock_manager)
    
    item = @family.gocardless_items.create!(
      name: "Legacy Bank",
      requisition_id: "legacy-provider-123",
      institution_id: "STARLING_SRLG"
      # No provider_name
    )
    
    account = @family.accounts.create!(
      name: "Legacy Account",
      accountable: Depository.new,
      balance: 1000
    )
    
    gocardless_account = item.gocardless_accounts.create!(
      gocardless_account_id: "legacy-account-123",
      account: account
    )
    
    # Test that the provider method falls back to the manager
    assert_equal mock_manager, gocardless_account.send(:provider)
  end
end
