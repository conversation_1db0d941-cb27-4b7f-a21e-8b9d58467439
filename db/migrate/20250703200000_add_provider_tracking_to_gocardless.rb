class AddProviderTrackingToGocardless < ActiveRecord::Migration[7.2]
  def change
    # Add provider tracking to gocardless_connections
    add_column :gocardless_connections, :provider_name, :string
    add_index :gocardless_connections, :provider_name
    
    # Add provider tracking to gocardless_items  
    add_column :gocardless_items, :provider_name, :string
    add_index :gocardless_items, :provider_name
    
    # Add comment explaining the provider_name field
    # Values should be: "primary", "backup", "backup2"
    # This tracks which GoCardless application was used to create the connection
    # so that future API calls use the correct credentials
  end
end
